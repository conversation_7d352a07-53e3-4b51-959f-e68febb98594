# 文件同步软件使用说明

## 功能特点
- 🔄 双向文件同步，自动选择最新版本
- 📁 支持单向复制（只有一个目录存在文件时）
- ⏰ 基于文件修改时间的智能同步
- 🌐 支持中文文件名和路径
- 📋 详细的同步日志输出

## 使用方法

### 1. 配置同步任务
编辑 `sync_config.json` 文件，按以下格式添加需要同步的文件：

```json
[
    {
        "file_name": "文件名.扩展名",
        "path1": "第一个路径",
        "path2": "第二个路径"
    },
    {
        "file_name": "另一个文件.txt",
        "path1": "C:\\路径1",
        "path2": "D:\\路径2"
    }
]
```

### 2. 运行同步程序
```bash
python file_sync.py
```

### 3. 同步规则
- **两个文件都存在**: 比较修改时间，用较新的覆盖较旧的
- **只有一个文件存在**: 复制到另一个目录
- **修改时间相同**: 显示"无需同步"
- **都不存在**: 提示错误

### 4. 输出格式
- 同步文件: `文件名 | 旧时间 -> 新时间`
- 无需同步: `文件名 | 无需同步`
- 新建文件: `文件名 | 新建 -> 创建时间`

## 注意事项
- 路径分隔符使用双反斜杠 `\\` 或正斜杠 `/`
- 程序会自动创建不存在的目录
- 支持相对路径和绝对路径
- 完成后按任意键退出程序

## 示例配置
程序包含了一个示例配置文件 `sync_config.json`，您可以根据需要修改路径和文件名。

# 文件同步软件项目结构

```
RuanKao_sync/
│
├── file_sync.py          # 主程序文件
├── sync_config.json      # 默认配置文件（用户可编辑）
├── demo_config.json      # 演示配置文件
├── 运行同步.bat          # Windows批处理启动文件
├── README.md             # 使用说明文档
├── venv/                 # Python虚拟环境
├── test_folder1/         # 测试文件夹1
│   ├── 测试文件.txt
│   ├── 独有文件.txt
│   └── 相同时间文件.txt
└── test_folder2/         # 测试文件夹2
    ├── 测试文件.txt
    ├── 独有文件.txt
    └── 相同时间文件.txt
```

## 快速开始

1. **编辑配置文件**: 修改 `sync_config.json` 设置您的同步路径
2. **运行程序**: 双击 `运行同步.bat` 或在命令行运行 `python file_sync.py`
3. **查看结果**: 程序会显示详细的同步日志

## 程序功能验证

已测试的功能场景：
- ✅ 双向文件同步（选择最新版本）
- ✅ 单向文件复制（只有一边存在文件）
- ✅ 相同时间文件跳过同步
- ✅ 文件不存在的错误处理
- ✅ 中文文件名和路径支持
- ✅ 自动创建目录
- ✅ 详细的同步日志输出
- ✅ 用户按键退出功能